# 🚀 上海荷阁科技 - GitLab CI/CD 配置
# 企业级 Next.js 应用的完整 CI/CD 流水线
# 支持容器化部署、安全扫描、自动化测试报告

# ==========================================
# 全局配置
# ==========================================

# 定义流水线阶段
stages:
  - prepare      # 环境准备和依赖安装
  - test         # 单元测试和集成测试
  - security     # 安全扫描和代码质量检查
  - build        # 构建应用和Docker镜像
  - deploy       # 部署到目标环境
  - report       # 生成和发布报告

# 全局变量
variables:
  # Node.js 版本
  NODE_VERSION: "18"
  # Docker 镜像仓库
  DOCKER_REGISTRY: "$CI_REGISTRY"
  DOCKER_IMAGE: "$CI_REGISTRY_IMAGE"
  # 应用配置
  APP_NAME: "hege-tech-web"
  APP_PORT: "3000"
  # 缓存配置
  CACHE_KEY: "$CI_COMMIT_REF_SLUG-$CI_COMMIT_SHA"
  # Windows 环境配置
  WINDOWS_SHELL: "powershell"

# 全局缓存配置
cache:
  key:
    files:
      - package-lock.json
  paths:
    - node_modules/
    - .next/cache/
  policy: pull-push

# Windows 环境默认配置
default:
  tags:
    - windows
    - shell
  before_script:
    - 'echo "🚀 开始执行 CI/CD 流水线 - $(Get-Date)"'
    - 'echo "📦 项目 - $env:APP_NAME"'
    - 'echo "🌿 分支 - $env:CI_COMMIT_REF_NAME"'
    - 'echo "📝 提交 - $env:CI_COMMIT_SHORT_SHA"'
    - 'echo "🔧 配置 npm 镜像源..."'
    - 'npm config set registry https://registry.npmmirror.com/'

# ==========================================
# 准备阶段 - 环境准备和依赖安装
# ==========================================

# Windows Runner 测试作业
test:windows-runner:
  stage: prepare
  script:
    - 'echo "🪟 测试 Windows Runner 连接..."'
    - 'echo "当前时间 - $(Get-Date)"'
    - 'echo "PowerShell 版本 - $($PSVersionTable.PSVersion)"'
    - 'echo "当前目录 - $(Get-Location)"'
    - 'echo "环境变量 CI_PROJECT_NAME - $env:CI_PROJECT_NAME"'
    - 'echo "✅ Windows Runner 测试完成"'
  only:
    - ci
    - main
    - develop

prepare:dependencies:
  stage: prepare
  script:
    - 'echo "📦 使用 PowerShell 脚本安装依赖..."'
    - 'PowerShell -ExecutionPolicy Bypass -File .cicd/scripts/install-dependencies.ps1'
    - 'echo "✅ 依赖安装完成"'
  cache:
    key:
      files:
        - package-lock.json
    paths:
      - node_modules/
      - .npm/
    policy: push
  artifacts:
    paths:
      - node_modules/
    expire_in: 1 hour
  only:
    changes:
      - package.json
      - package-lock.json
      - "**/*.js"
      - "**/*.ts"
      - "**/*.tsx"

# ==========================================
# 测试阶段 - 单元测试和集成测试
# ==========================================

test:unit:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - 'echo "🧪 运行单元测试..."'
    - 'npm run test:ci'
    - 'echo "📊 生成测试覆盖率报告..."'
    - 'npm run test:coverage'
  coverage: '/All files[^|]*\|[^|]*\s+([\d\.]+)/'
  artifacts:
    when: always
    reports:
      junit: coverage/junit.xml
      coverage_report:
        coverage_format: cobertura
        path: coverage/cobertura-coverage.xml
    paths:
      - coverage/
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

test:lint:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - 'echo "🔍 运行代码检查..."'
    - 'npm run lint'
    - 'echo "🎨 检查代码格式..."'
    - 'npm run format:check'
  artifacts:
    when: on_failure
    paths:
      - lint-results.json
    expire_in: 1 week
  only:
    - main
    - develop
    - merge_requests

test:type-check:
  stage: test
  dependencies:
    - prepare:dependencies
  script:
    - 'echo "🔧 TypeScript 类型检查..."'
    - 'npm run type-check'
  only:
    - main
    - develop
    - merge_requests

# ==========================================
# 安全扫描阶段
# ==========================================

security:sast:
  stage: security
  dependencies:
    - prepare:dependencies
  script:
    - 'echo "🔒 运行静态应用安全测试 (SAST)..."'
    - 'npm audit --audit-level=moderate'
    - 'echo "🔍 ESLint 安全规则检查..."'
    - 'npx eslint . --config .cicd/security/sast/.eslintrc.security.js --format json --output-file sast-results.json || true'
  artifacts:
    when: always
    reports:
      sast: sast-results.json
    paths:
      - sast-results.json
    expire_in: 1 week
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

security:dependency-scan:
  stage: security
  dependencies:
    - prepare:dependencies
  script:
    - 'echo "🔍 依赖漏洞扫描..."'
    - 'npm audit --json > dependency-scan.json || true'
    - 'echo "📋 生成依赖报告..."'
    - 'npm ls --depth=0 > dependencies-list.txt'
  artifacts:
    when: always
    reports:
      dependency_scanning: dependency-scan.json
    paths:
      - dependency-scan.json
      - dependencies-list.txt
    expire_in: 1 week
  allow_failure: true
  only:
    - main
    - develop
    - merge_requests

# ==========================================
# 构建阶段
# ==========================================

build:app:
  stage: build
  dependencies:
    - prepare:dependencies
  script:
    - 'echo "🏗️ 构建 Next.js 应用..."'
    - 'npm run build'
    - 'echo "📦 构建完成，生成静态文件"'
    - 'Get-ChildItem .next\ -Force | Format-Table Name, Length, LastWriteTime'
  artifacts:
    paths:
      - .next/
      - out/
    expire_in: 1 day
  only:
    - main
    - develop
    - merge_requests

build:docker:
  stage: build
  dependencies:
    - build:app
  variables:
    # 本地 Docker 镜像标签
    LOCAL_IMAGE_NAME: "hege-tech-web"
    LOCAL_IMAGE_TAG: "$CI_COMMIT_SHORT_SHA"
  before_script:
    - 'echo "检查 Docker 环境..."'
    - 'docker --version'
    - 'docker info'
    - 'echo "当前 Docker 镜像列表"'
    - 'docker images'
  script:
    - 'echo "构建 Docker 镜像到本地..."'
    - '$imageName = $env:LOCAL_IMAGE_NAME'
    - '$imageTag = $env:LOCAL_IMAGE_TAG'
    - 'docker build -f .cicd/docker/Dockerfile -t "${imageName}:${imageTag}" -t "${imageName}:latest" .'
    - 'echo "Docker 镜像构建完成"'
    - 'echo "构建后的镜像列表"'
    - 'docker images ${imageName}'
    - 'echo "镜像详细信息"'
    - 'docker inspect "${imageName}:latest" | Select-String -Pattern "ExposedPorts" || echo "无暴露端口信息"'
    - 'docker inspect "${imageName}:latest" | Select-String -Pattern "Env" | Select-Object -First 5 || echo "无环境变量信息"'
    - 'echo "生成构建报告..."'
    - 'PowerShell -ExecutionPolicy Bypass -File .cicd/scripts/generate-docker-report.ps1 -ImageName ${imageName} -ImageTag "latest"'
  artifacts:
    reports:
      # 生成镜像构建报告
      junit: docker-build-report.xml
    paths:
      - docker-build-report.xml
    expire_in: 1 day
    when: always
  only:
    - main
    - develop
    - ci

# ==========================================
# 部署阶段
# ==========================================

deploy:local:
  stage: deploy
  dependencies:
    - build:docker
  variables:
    LOCAL_IMAGE_NAME: "hege-tech-web"
    CONTAINER_NAME: "hege-tech-web-local"
    CONTAINER_PORT: "3000"
    HOST_PORT: "3000"
  environment:
    name: local
    url: http://localhost:3000
  script:
    - 'echo "🚀 部署到本地 Docker 环境..."'
    - '$imageName = $env:LOCAL_IMAGE_NAME'
    - '$containerName = $env:CONTAINER_NAME'
    - '$hostPort = $env:HOST_PORT'
    - '$containerPort = $env:CONTAINER_PORT'
    - 'echo "🛑 停止并删除现有容器（如果存在）..."'
    - 'docker stop ${containerName} 2>$null || echo "容器不存在或已停止"'
    - 'docker rm ${containerName} 2>$null || echo "容器不存在"'
    - 'echo "🚀 启动新容器..."'
    - 'docker run -d --name ${containerName} -p "${hostPort}:${containerPort}" "${imageName}:latest"'
    - 'echo "⏳ 等待容器启动..."'
    - 'Start-Sleep -Seconds 15'
    - 'echo "🔍 验证部署状态..."'
    - 'PowerShell -ExecutionPolicy Bypass -File .cicd/scripts/verify-local-deployment.ps1 -ContainerName ${containerName} -HostPort ${hostPort}'
    - 'echo "✅ 本地部署完成"'
    - 'echo "🌐 应用访问地址: http://localhost:${hostPort}"'
  after_script:
    - 'echo "📊 部署后状态检查..."'
    - '$containerName = $env:CONTAINER_NAME'
    - 'docker ps -a -f name=${containerName} --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"'
    - 'echo "📋 最新容器日志（最近10行）"'
    - 'docker logs --tail 10 ${containerName} 2>$null || echo "无法获取日志"'
  only:
    - main
    - develop
    - ci
  when: manual

# 清理本地容器的作业
cleanup:local:
  stage: deploy
  script:
    - 'echo "🧹 清理本地 Docker 环境..."'
    - 'echo "🛑 停止所有 hege-tech-web 相关容器..."'
    - 'docker ps -q -f name=hege-tech-web | ForEach-Object { docker stop $_ } 2>$null || echo "没有运行中的容器"'
    - 'echo "🗑️ 删除所有 hege-tech-web 相关容器..."'
    - 'docker ps -aq -f name=hege-tech-web | ForEach-Object { docker rm $_ } 2>$null || echo "没有容器需要删除"'
    - 'echo "🗑️ 清理未使用的镜像..."'
    - 'docker image prune -f'
    - 'echo "✅ 清理完成"'
  only:
    - main
    - develop
    - ci
  when: manual
