# 🚀 上海荷阁科技 - Windows 环境依赖安装脚本
# 解决 npm 依赖解析问题的多重策略

$ErrorActionPreference = "Stop"

Write-Host "📦 开始安装项目依赖..." -ForegroundColor Green

try {
    # 策略1: 使用国内镜像源
    Write-Host "🔧 配置 npm 镜像源..." -ForegroundColor Yellow
    npm config set registry https://registry.npmmirror.com/
    npm config set "@types:registry" https://registry.npmmirror.com/
    npm config set "@testing-library:registry" https://registry.npmmirror.com/

    # 策略2: 清理缓存
    Write-Host "🧹 清理 npm 缓存..." -ForegroundColor Yellow
    npm cache clean --force

    # 策略3: 尝试标准安装
    Write-Host "📦 尝试标准依赖安装..." -ForegroundColor Yellow
    $result = npm ci --no-audit --no-fund 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 标准安装成功" -ForegroundColor Green
        exit 0
    }

    # 策略4: 使用 legacy peer deps
    Write-Host "⚠️ 标准安装失败，尝试使用 legacy peer deps..." -ForegroundColor Yellow
    $result = npm ci --legacy-peer-deps --no-audit --no-fund 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ Legacy peer deps 安装成功" -ForegroundColor Green
        exit 0
    }

    # 策略5: 强制安装
    Write-Host "⚠️ Legacy peer deps 安装失败，尝试强制安装..." -ForegroundColor Yellow
    $result = npm ci --force --no-audit --no-fund 2>&1
    if ($LASTEXITCODE -eq 0) {
        Write-Host "✅ 强制安装成功" -ForegroundColor Green
        exit 0
    }

    # 策略6: 删除 node_modules 重新安装
    Write-Host "⚠️ 强制安装失败，删除 node_modules 重新安装..." -ForegroundColor Yellow
    if (Test-Path "node_modules") {
        Remove-Item -Recurse -Force "node_modules"
    }
    if (Test-Path "package-lock.json") {
        Remove-Item -Force "package-lock.json"
    }
    npm install --legacy-peer-deps --no-audit --no-fund

    Write-Host "✅ 依赖安装完成" -ForegroundColor Green
}
catch {
    Write-Host "❌ 依赖安装失败: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}
