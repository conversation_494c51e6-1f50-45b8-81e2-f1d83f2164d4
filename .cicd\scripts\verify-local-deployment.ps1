# 🚀 上海荷阁科技 - 本地部署验证脚本
# 验证本地 Docker 容器部署是否成功

param(
    [string]$ContainerName = "hege-tech-web-local",
    [string]$HostPort = "3000",
    [int]$MaxRetries = 30,
    [int]$RetryInterval = 2
)

$ErrorActionPreference = "Continue"

Write-Host "🔍 开始验证本地部署..." -ForegroundColor Green

# 检查容器是否运行
Write-Host "📋 检查容器状态..." -ForegroundColor Yellow
$containerStatus = docker ps --filter "name=$ContainerName" --format "{{.Status}}"

if (-not $containerStatus) {
    Write-Host "❌ 容器 $ContainerName 未运行" -ForegroundColor Red
    exit 1
}

Write-Host "✅ 容器状态: $containerStatus" -ForegroundColor Green

# 检查端口映射
Write-Host "📋 检查端口映射..." -ForegroundColor Yellow
$portMapping = docker port $ContainerName 2>$null
if ($portMapping) {
    Write-Host "✅ 端口映射: $portMapping" -ForegroundColor Green
} else {
    Write-Host "⚠️ 无法获取端口映射信息" -ForegroundColor Yellow
}

# 健康检查 - 尝试访问应用
Write-Host "🌐 进行健康检查..." -ForegroundColor Yellow
$healthCheckUrl = "http://localhost:$HostPort"

for ($i = 1; $i -le $MaxRetries; $i++) {
    try {
        Write-Host "尝试 $i/$MaxRetries : 访问 $healthCheckUrl" -ForegroundColor Cyan
        
        $response = Invoke-WebRequest -Uri $healthCheckUrl -TimeoutSec 5 -UseBasicParsing
        
        if ($response.StatusCode -eq 200) {
            Write-Host "✅ 健康检查成功! HTTP 状态码: $($response.StatusCode)" -ForegroundColor Green
            Write-Host "📄 响应内容长度: $($response.Content.Length) 字符" -ForegroundColor Green
            
            # 检查响应内容是否包含预期的内容
            if ($response.Content -match "Next.js|React|荷阁科技") {
                Write-Host "✅ 响应内容验证成功" -ForegroundColor Green
            } else {
                Write-Host "⚠️ 响应内容可能不完整" -ForegroundColor Yellow
            }
            
            break
        }
    }
    catch {
        Write-Host "❌ 尝试 $i 失败: $($_.Exception.Message)" -ForegroundColor Red
        
        if ($i -eq $MaxRetries) {
            Write-Host "❌ 健康检查失败，已达到最大重试次数" -ForegroundColor Red
            
            # 显示容器日志以便调试
            Write-Host "📋 容器日志（最近50行）:" -ForegroundColor Yellow
            docker logs --tail 50 $ContainerName
            
            exit 1
        }
        
        Write-Host "⏳ 等待 $RetryInterval 秒后重试..." -ForegroundColor Yellow
        Start-Sleep -Seconds $RetryInterval
    }
}

# 显示最终状态
Write-Host "`n📊 部署验证完成!" -ForegroundColor Green
Write-Host "🌐 应用访问地址: $healthCheckUrl" -ForegroundColor Cyan
Write-Host "📋 容器信息:" -ForegroundColor Yellow
docker ps --filter "name=$ContainerName" --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}\t{{.Image}}"

# 显示资源使用情况
Write-Host "`n📊 资源使用情况:" -ForegroundColor Yellow
docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}\t{{.NetIO}}" $ContainerName

Write-Host "`n✅ 本地部署验证成功!" -ForegroundColor Green
