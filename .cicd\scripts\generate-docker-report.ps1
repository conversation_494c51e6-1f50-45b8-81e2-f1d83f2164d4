# 🚀 上海荷阁科技 - Docker 构建报告生成脚本
# 生成 JUnit 格式的 Docker 构建报告

param(
    [string]$ImageName = "hege-tech-web",
    [string]$ImageTag = "latest",
    [string]$OutputFile = "docker-build-report.xml"
)

$ErrorActionPreference = "Continue"

Write-Host "📊 生成 Docker 构建报告..." -ForegroundColor Green

# 检查镜像是否存在
$imageExists = docker images --format "{{.Repository}}:{{.Tag}}" | Select-String "${ImageName}:${ImageTag}"

if ($imageExists) {
    $testResult = "passed"
    $failureMessage = ""
    Write-Host "✅ Docker 镜像构建成功" -ForegroundColor Green
} else {
    $testResult = "failed"
    $failureMessage = "Docker 镜像构建失败或镜像不存在"
    Write-Host "❌ Docker 镜像构建失败" -ForegroundColor Red
}

# 获取镜像信息
try {
    $imageInfo = docker inspect "${ImageName}:${ImageTag}" 2>$null | ConvertFrom-Json
    $imageSize = docker images --format "{{.Size}}" --filter "reference=${ImageName}:${ImageTag}"
    $createdTime = $imageInfo.Created
    $architecture = $imageInfo.Architecture
    $os = $imageInfo.Os
} catch {
    $imageSize = "未知"
    $createdTime = "未知"
    $architecture = "未知"
    $os = "未知"
}

# 生成 JUnit XML 报告
$junitXml = @"
<?xml version="1.0" encoding="UTF-8"?>
<testsuites name="Docker Build Report" tests="1" failures="$(if($testResult -eq 'failed'){1}else{0})" time="0">
  <testsuite name="Docker Image Build" tests="1" failures="$(if($testResult -eq 'failed'){1}else{0})" time="0">
    <testcase name="Build ${ImageName}:${ImageTag}" classname="DockerBuild" time="0">
$(if($testResult -eq 'failed'){
"      <failure message=`"$failureMessage`">$failureMessage</failure>"
}else{
"      <system-out>
镜像名称: ${ImageName}:${ImageTag}
镜像大小: $imageSize
创建时间: $createdTime
架构: $architecture
操作系统: $os
构建状态: 成功
      </system-out>"
})
    </testcase>
  </testsuite>
</testsuites>
"@

# 写入报告文件
$junitXml | Out-File -FilePath $OutputFile -Encoding UTF8

Write-Host "📋 Docker 构建报告已生成: $OutputFile" -ForegroundColor Green
Write-Host "📊 报告内容预览:" -ForegroundColor Yellow
Get-Content $OutputFile | Select-Object -First 10

# 显示镜像列表
Write-Host "`n📋 当前 Docker 镜像列表:" -ForegroundColor Yellow
docker images $ImageName --format "table {{.Repository}}\t{{.Tag}}\t{{.Size}}\t{{.CreatedAt}}"
